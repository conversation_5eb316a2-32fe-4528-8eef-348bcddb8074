import re


def to_gmf_admonition(lines: list[str]):
    output = []
    last_speaker = None
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        print(f"Processing line: '{line}'")
        
        if ':' in line or "：" in line:
            speaker, content = re.split(r'[:：]', line, 1)
            speaker = speaker.strip('* ').capitalize()  # 首字母大写
            content = content.strip()
            print(f"  Speaker: '{speaker}', Content: '{content}'")
        else:
            speaker, content = '', line.strip()
            print(f"  No speaker found, content: '{content}'")

        if content == '':
            continue
        
        if speaker != last_speaker:
            if last_speaker is not None:
                output.append('')  # 插入空行
            if speaker.lower() == 'flora':
                output.append('>[!tip] Flora: ' + content)
                print(f"  Added Flora admonition")
            elif speaker.lower() == 'aaron':
                output.append('>[!note] Aaron: ' + content)
                print(f"  Added Aaron admonition")
            else:
                output.append('> ' + line.strip())
                print(f"  Added generic line")
        else:
            output.append('> ' + content)
            print(f"  Added continuation")
        last_speaker = speaker

    return output

# Test with our sample data
with open('test_script.txt', 'r', encoding='utf-8') as f:
    lines = f.readlines()

output = to_gmf_admonition(lines)
print("\nFinal output:")
for line in output:
    print(line)
