<link href="assets/css/bootstrap.min.4.0.css" rel="stylesheet" />
<link href="assets/css/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet" />
<meta name="viewport" content="width=device-width, initial-scale=1">


<style>
  .md-typeset h1,
  .md-content__button {
    display: none;
  }

.md-typeset hr {
    display: none;
}

.as-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(360px, 1fr));
}



@media (min-width: 768px) { 
    .card-columns {
        column-count: 2;
    }
 }

@media (min-width: 1200px) { 
    .card-columns {
        column-count: 3;
    }

    .md-sidebar--primary {
    display: none;
    }
 }

a .card-title {
    color: rgb(55, 58, 60);
    font-size: 17px;
}

a .card-text {
    color: rgb(55, 58, 60);
    font-size: 14px;
}

a:hover {
    color: inherit;
    text-decoration: inherit;
}

nav a {
    font-size: 0.8rem !important;
    color: white;
    mix-blend-mode: difference;
}
</style>

<div class="as-grid m-t-md">
<div class="card-columns">
    
<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/algo/pdf-is-all-you-need-2/">
    <img class="card-img-top img-responsive" src="https://cdn.jsdelivr.net/gh/zillionare/imgbed2@main/images/2025/07/haley-phelps-S-llxYh3GzI-unsplash.jpg"/>
    <div class="card-body">
        <h4 class="card-title">PDF is all you need(2)</h4>
        <p class="card-text">从赌场游戏到数学家们的战争，概率论如何摆脱直觉的束缚？柯尔莫哥洛夫的公理化革命，让微积分成为概率论的新武器，彻底改写了我们对随机世界的理解</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-07-31</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/algo/pdf-is-all-you-need/">
    <img class="card-img-top img-responsive" src="https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/banff-sunshine-village-UoBE_wJ-suk-unsplash.jpg"/>
    <div class="card-body">
        <h4 class="card-title">量化面试神题：圆上随机点的概率陷阱</h4>
        <p class="card-text">常常有人问，做量化交易需要什么样的数学基础？<br><br>不同领域的量化研究员需要数学基础是不一样的，一般是期权 > 高频 > 期货 > 股票中低频量化。<br><br>今天我们就聊聊各个领域要求的数学基础，并且以一道经典的量化面试题（来自绿皮书），介绍在概率知识上，从低...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-07-24</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/tools/quantstats-reload-news/">
    <img class="card-img-top img-responsive" src="https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/kamil-pietrzak-AlA8S9tALAs-unsplash.jpg"/>
    <div class="card-body">
        <h4 class="card-title">原作者失联8个月，我们接手维护后他突然回来了</h4>
        <p class="card-text">Quantstats是非常著名的量化策略评估与可视化库。从2024年底起约8个月里，它没有得到积极的维护，出现了在Python 3.12以上，完全无法运行等严重bug。好消息是，最近一周，原作者 Ran Aroussi 已经恢复了对这个库的维护，并且连发了5个版本（从0.0...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-07-23</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/papers/04-年化25%的策略为何翻车/">
    <img class="card-img-top img-responsive" src="https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/neom-diving.jpg"/>
    <div class="card-body">
        <h4 class="card-title">『译研报04』 年化25%的策略到底有没有翻车？</h4>
        <p class="card-text">LLT均线策略回测结果好到难以置信，年化收益竟达25%！但当我们用显微镜审视每个细节时，发现了隐藏的致命陷阱：未来数据泄露、信号对齐错误、交易成本设置不当...这些看似微小的技术细节，正是导致『回测买地球、实盘亏成狗』的真正元凶</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-07-21</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/papers/03-低延迟趋势线与交易择时/">
    <img class="card-img-top img-responsive" src="https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/07/neom-tSwRu3Jh0EM-unsplash.jpg"/>
    <div class="card-body">
        <h4 class="card-title">『译研报03』Z变换改造均线，一个12年前的策略为何仍能跑赢大盘？</h4>
        <p class="card-text">传统移动平均线（MA）是技术分析中常用的趋势跟踪指标，通过对股票价格或指数在一定天数内的平均值进行计算，以刻画其变动方向。MA 的计算天数越多，其平滑性越好，但随之而来的时滞（延迟）影响也越严重。这意味着 MA 指标在跟踪趋势时容易出现“跟不紧”甚至“跟不上”的情况，平滑性...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-07-16</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/python/it-is-π-thon/">
    <img class="card-img-top img-responsive" src="https://cdn.jsdelivr.net/gh/zillionare/images@main/images/hot/meme/π-thon.png"/>
    <div class="card-body">
        <h4 class="card-title">π-thon以及他的朋友们</h4>
        <p class="card-text">最近的Python社区热闹异常。在6月中旬，Python发布了Python 3.14 beta3。它可不是一个普通的预发布版本 -- 它是第一个正式支持期待已久的自由线程或『无 GIL』的版本。而有没有GIL，绝对是Python发展史上的有一个分水岭。这个版本将在今年的程序...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-07-15</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/papers/ubl-2/">
    <img class="card-img-top img-responsive" src="https://images.jieyu.ai/images/university/Mackey_Auditorium-Colorado.jpg"/>
    <div class="card-body">
        <h4 class="card-title">『匡醍译研报 02』 驯龙高手，从股谚到量化因子的工程化落地</h4>
        <p class="card-text">上一期文章中，我们复现了研报的因子构建部分，分别是影线因子、威廉影线因子以及由此组合而来的 UBL 因子。这一期我们将对这些因子进行检验。<br><br>因子检验固然是因子挖掘中必不可少的一环，但它应该是一个 routine 的工作 -- 我们不应该每次都重新发明轮子。然而...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-07-04</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/papers/ubl/">
    <img class="card-img-top img-responsive" src="https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/06/dragons.png"/>
    <div class="card-body">
        <h4 class="card-title">『匡醍译研报 01』 驯龙高手，从股谚到量化因子的工程化落地</h4>
        <p class="card-text">头上三柱香，不死也赔光。这是一句股谚，说得是如果在高位出现三根长上影线，那么股价短期内很可能会下跌。因为上影线代表了上面的抛压特别大。这种说法能得到统计数据上的验证吗？来自东吴证券的一份研报，就讨论了这个问题。<br><br>这份研报给出一个很价值的结论，那就是，影线好不好...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-06-29</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/factor-strategy/构建强化学习交易模型/">
    <img class="card-img-top img-responsive" src="https://cdn.jsdelivr.net/gh/zillionare/images@main/images/2025/06/20250625204449.png"/>
    <div class="card-body">
        <h4 class="card-title">强化学习模型能否自我演化出交易智慧？</h4>
        <p class="card-text">!!! abstract 内容摘要<br>    * 强化学习已在摩根大通及全球顶级投资机构中使用。<br>    * 与监督学习不同，强化学习不会在每一步都只接受标准答案，它会尝试、忍受短期的损失，博取长期的收益。这就使得它有了对抗金融数据噪声的能力。<br>    * ...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-06-25</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/tools/quantstats-reloaded/">
    <img class="card-img-top img-responsive" src="https://images.jieyu.ai/images/university/university-college-london-library.jpg"/>
    <div class="card-body">
        <h4 class="card-title">Quantstats Reloaded</h4>
        <p class="card-text">Quantstats 是一款用于交易策略绩效分析的 Python 库，深受量化圈用户喜爱，在 Github 上获得了超过 5.8k 的 stars。但很遗憾，由于原作者长期未维护，现在新安装的 Quantstats，尤其是在 Python 3.12 及以上高版本中，几乎无法...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-06-16</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/tools/21天驯化AI打工仔/8_QMT实时分钟线数据订阅系统/">
    <img class="card-img-top img-responsive" src="https://images.jieyu.ai/images/hot/mybook/screen-shot-and-book.png"/>
    <div class="card-body">
        <h4 class="card-title">21 天驯化 AI 打工仔: QMT 实时分笔数据订阅系统与多 Client 问题</h4>
        <p class="card-text">> 当数据如潮水般涌来，如何让系统稳如磐石？本文带你深入 QMT 实时数据订阅的世界，见证 007 助手如何将一个简单的数据获取程序，升级为处理能力提升 10 倍的高性能系统！<br><br>"007，我们的日线数据定时获取系统已经很稳定了，但现在我需要更细粒度的数据——分...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-06-15</small></p>
    </div>
    </a>
</div><!--end-card-->


<div class="card">
    <a href="https://www.jieyu.ai/blog/posts/tools/21天驯化AI打工仔/9_系统逻辑优化与分钟线数据合成/">
    <img class="card-img-top img-responsive" src="https://images.jieyu.ai/images/university/university-college-london-library.jpg"/>
    <div class="card-body">
        <h4 class="card-title">21 天驯化 AI 打工仔:系统逻辑优化与分钟线数据合成</h4>
        <p class="card-text">> 当分笔数据如潮水般涌来，如何让系统智能地将它们合成为有价值的分钟线数据？本文带你深入量化交易系统的核心——数据合成与系统架构优化的世界！<br><br>"007，我们的实时分笔数据订阅系统已经基本完成，但现在我遇到了一个新的挑战。"我一边查看着 Redis 中堆积如山的...</p>
        <p class="card-text"><small class="text-muted"><i class="fa fa-calendar"></i>2025-06-15</small></p>
    </div>
    </a>
</div><!--end-card-->

</div>
</div>


